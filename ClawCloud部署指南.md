# ClawCloud 部署指南

## 项目概述
这是一个基于FastAPI的代理服务器项目，运行在端口9080上。

## 部署步骤

### 1. 准备工作
确保您的项目文件完整：
- `Dockerfile` ✅
- `proxy_server.py` ✅ 
- `requirements.txt` ✅
- `lmaren.js` ✅

### 2. 在ClawCloud上创建应用

#### 方法一：使用App Launchpad（推荐）
1. 在ClawCloud控制台中，点击 **App Launchpad**
2. 选择 **"Create New App"** 或 **"Deploy from Git"**
3. 如果选择从Git部署：
   - 输入您的Git仓库URL
   - 选择分支（通常是main或master）
   - ClawCloud会自动检测到Dockerfile

#### 方法二：使用Terminal手动部署
1. 点击 **Terminal** 图标
2. 克隆您的代码仓库：
```bash
git clone <您的仓库URL>
cd lmarena-proxy-server
```

### 3. 配置部署参数

#### 端口配置
- **容器端口**: 9080 (已在Dockerfile中EXPOSE)
- **外部访问端口**: ClawCloud会自动分配

#### 环境变量（如需要）
在部署配置中添加必要的环境变量，例如：
```
API_KEYS=your_api_keys_here
LOG_LEVEL=INFO
```

#### 资源配置
根据您的需求选择：
- **CPU**: 建议至少1核
- **内存**: 建议至少512MB-1GB
- **存储**: 根据日志和数据需求

### 4. 构建和部署

#### 使用App Launchpad自动部署
1. 配置完成后点击 **"Deploy"**
2. ClawCloud会自动：
   - 拉取代码
   - 构建Docker镜像
   - 启动容器
   - 分配外部URL

#### 使用Terminal手动部署
```bash
# 构建Docker镜像
docker build -t lmarena-proxy .

# 运行容器
docker run -d -p 9080:9080 --name lmarena-proxy lmarena-proxy
```

### 5. 访问您的应用

部署成功后，您会获得一个类似这样的URL：
```
https://your-app-name.ap-southeast-1.run.clawcloud
```

### 6. 监控和管理

#### 查看日志
- 在App Launchpad中点击您的应用
- 选择 **"Logs"** 标签页查看实时日志

#### 应用管理
- **启动/停止**: 在应用详情页面控制
- **重新部署**: 推送新代码后自动触发
- **扩容**: 根据需要调整资源配置

### 7. 域名和SSL（可选）

ClawCloud提供：
- 自动SSL证书
- 自定义域名绑定
- 负载均衡

### 8. 故障排除

#### 常见问题
1. **端口问题**: 确保Dockerfile中EXPOSE的端口与应用监听端口一致
2. **依赖问题**: 检查requirements.txt是否包含所有必需的包
3. **启动失败**: 查看日志了解具体错误信息

#### 调试命令
```bash
# 查看容器状态
docker ps

# 查看容器日志
docker logs <container_id>

# 进入容器调试
docker exec -it <container_id> /bin/bash
```

### 9. 性能优化建议

1. **使用多进程**: 在生产环境中考虑使用gunicorn
2. **缓存策略**: 实现适当的缓存机制
3. **监控指标**: 利用内置的Prometheus指标
4. **日志管理**: 配置合适的日志级别和轮转

### 10. 安全考虑

1. **API密钥管理**: 使用环境变量存储敏感信息
2. **CORS配置**: 确保CORS设置符合安全要求
3. **访问控制**: 实现适当的认证和授权机制

## 快速部署命令

如果您想快速开始，可以使用以下步骤：

1. 在ClawCloud Terminal中：
```bash
git clone <您的仓库URL>
cd lmarena-proxy-server
docker build -t lmarena-proxy .
docker run -d -p 9080:9080 lmarena-proxy
```

2. 或者直接使用App Launchpad的Git集成功能，输入仓库URL即可自动部署。

## 联系支持

如果遇到问题，可以：
- 查看ClawCloud文档
- 联系ClawCloud技术支持
- 检查应用日志获取详细错误信息
